const http = require('http');

// Test backend endpoints
const testEndpoints = [
    { path: '/health', method: 'GET', description: 'Health check' },
    { path: '/dev/tracks', method: 'GET', description: 'Get all tracks' },
    { path: '/dev/auth/me', method: 'GET', description: 'Get current user' },
    { path: '/dev/auth/login', method: 'POST', description: 'User login',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpass'
      })
    },
    { path: '/dev/admin/stats', method: 'GET', description: 'Get platform stats' }
];

function testEndpoint(endpoint) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: endpoint.path,
            method: endpoint.method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        if (endpoint.body) {
            options.headers['Content-Length'] = Buffer.byteLength(endpoint.body);
        }

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        endpoint: endpoint.description,
                        status: res.statusCode,
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        data: jsonData
                    });
                } catch (e) {
                    resolve({
                        endpoint: endpoint.description,
                        status: res.statusCode,
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        data: data
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject({
                endpoint: endpoint.description,
                error: err.message,
                success: false
            });
        });

        if (endpoint.body) {
            req.write(endpoint.body);
        }
        
        req.end();
    });
}

async function runTests() {
    console.log('🎵 TUNAMI Backend API Tests');
    console.log('============================\n');

    for (const endpoint of testEndpoints) {
        try {
            console.log(`Testing: ${endpoint.description}`);
            const result = await testEndpoint(endpoint);
            
            if (result.success) {
                console.log(`✅ ${endpoint.description} - Status: ${result.status}`);
                if (endpoint.path === '/health') {
                    console.log(`   Backend Version: ${result.data.version}`);
                    console.log(`   Features: ${result.data.features.join(', ')}`);
                } else if (endpoint.path === '/dev/tracks') {
                    console.log(`   Found ${result.data.data?.tracks?.length || 0} tracks`);
                    if (result.data.data?.tracks?.length > 0) {
                        console.log(`   Sample track: "${result.data.data.tracks[0].title}"`);
                    }
                } else if (endpoint.path === '/dev/auth/me') {
                    console.log(`   User: ${result.data.data?.username} (${result.data.data?.email})`);
                } else if (endpoint.path === '/dev/admin/stats') {
                    console.log(`   Total Users: ${result.data.data?.totalUsers}`);
                    console.log(`   Total Tracks: ${result.data.data?.totalTracks}`);
                    console.log(`   Total Reports: ${result.data.data?.totalReports}`);
                } else if (endpoint.path === '/dev/auth/login') {
                    console.log(`   Login successful for: ${result.data.data?.user?.username}`);
                }
            } else {
                console.log(`❌ ${endpoint.description} - Status: ${result.status}`);
                console.log(`   Error: ${JSON.stringify(result.data, null, 2)}`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint.description} - Connection Error`);
            console.log(`   Error: ${error.error || error.message}`);
        }
        console.log('');
    }

    console.log('🎯 Test Summary');
    console.log('================');
    console.log('If you see connection errors, make sure the backend is running:');
    console.log('   node scripts/local-backend-full.js');
    console.log('');
    console.log('Backend should be accessible at: http://localhost:3001');
    console.log('Frontend test UI is available at: file:///' + __dirname.replace(/\\/g, '/') + '/test-ui.html');
}

// Run the tests
runTests().catch(console.error);
