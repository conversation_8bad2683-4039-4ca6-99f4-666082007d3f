@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-gray-50 via-white to-tunami-50/30 text-dark-900 antialiased;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-tunami-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-tunami-400;
  }
}

@layer components {
  /* Modern Button System */
  .btn {
    @apply px-4 py-2.5 rounded-xl font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .btn-primary {
    @apply btn bg-gradient-to-r from-tunami-600 to-tunami-700 text-white hover:from-tunami-700 hover:to-tunami-800 focus:ring-tunami-500 shadow-tunami hover:shadow-tunami-lg;
  }

  .btn-secondary {
    @apply btn bg-gradient-to-r from-electric-500 to-electric-600 text-white hover:from-electric-600 hover:to-electric-700 focus:ring-electric-500 shadow-electric hover:shadow-lg;
  }

  .btn-ai {
    @apply btn bg-gradient-to-r from-ai-600 to-ai-700 text-white hover:from-ai-700 hover:to-ai-800 focus:ring-ai-500 shadow-ai hover:shadow-lg;
  }

  .btn-outline {
    @apply btn border-2 border-tunami-300 text-tunami-700 hover:bg-tunami-50 hover:border-tunami-400 focus:ring-tunami-500 backdrop-blur-sm;
  }

  .btn-ghost {
    @apply btn text-tunami-600 hover:bg-tunami-50 hover:text-tunami-700 focus:ring-tunami-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm rounded-lg;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg rounded-2xl;
  }

  /* Modern Input System */
  .input {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-tunami-500 focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm placeholder-gray-400;
  }

  .input-error {
    @apply input border-error-300 focus:ring-error-500 bg-error-50/50;
  }

  .input-success {
    @apply input border-success-300 focus:ring-success-500 bg-success-50/50;
  }

  .input-floating {
    @apply input pt-6 pb-2;
  }

  /* Labels and Text */
  .label {
    @apply block text-sm font-semibold text-dark-700 mb-2;
  }

  .label-floating {
    @apply absolute left-4 top-2 text-xs font-medium text-gray-500 transition-all duration-200;
  }

  .error-text {
    @apply text-sm text-error-600 mt-1 font-medium;
  }

  .success-text {
    @apply text-sm text-success-600 mt-1 font-medium;
  }

  /* Modern Card System */
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 transition-all duration-300 hover:shadow-xl hover:shadow-tunami/10;
  }

  .card-interactive {
    @apply card hover:scale-[1.02] cursor-pointer;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-white via-white to-tunami-50/30 border-tunami-100;
  }

  .card-ai {
    @apply bg-gradient-to-br from-ai-50 to-ai-100/50 border-ai-200;
  }

  .card-electric {
    @apply bg-gradient-to-br from-electric-50 to-electric-100/50 border-electric-200;
  }

  /* Layout Components */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section {
    @apply py-12 lg:py-16;
  }

  /* Modern Navigation */
  .nav-link {
    @apply flex items-center space-x-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105;
  }

  .nav-link-active {
    @apply nav-link bg-gradient-to-r from-tunami-100 to-tunami-200 text-tunami-800 shadow-tunami;
  }

  .nav-link-inactive {
    @apply nav-link text-dark-600 hover:text-dark-900 hover:bg-gray-100;
  }

  /* Music Player Styles */
  .player-control {
    @apply w-12 h-12 rounded-full bg-gradient-to-r from-tunami-600 to-tunami-700 text-white flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-glow active:scale-95;
  }

  .progress-bar {
    @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-tunami-500 to-electric-500 transition-all duration-300;
  }

  /* Track Card Styles */
  .track-card {
    @apply card-interactive group relative overflow-hidden;
  }

  .track-card-image {
    @apply w-16 h-16 rounded-xl bg-gradient-to-br from-tunami-400 to-ai-500 flex items-center justify-center text-white font-bold text-lg shadow-lg;
  }

  /* Loading and Animation Styles */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-tunami-600;
  }

  .skeleton {
    @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] rounded-lg;
  }

  /* Utility Classes */
  .text-gradient {
    @apply bg-gradient-to-r from-tunami-600 via-ai-600 to-electric-600 bg-clip-text text-transparent;
  }

  .text-gradient-ai {
    @apply bg-gradient-to-r from-ai-600 to-tunami-600 bg-clip-text text-transparent;
  }

  .border-gradient {
    @apply border-2 border-transparent bg-gradient-to-r from-tunami-500 to-ai-500 bg-clip-border;
  }

  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-dark-900/10 backdrop-blur-md border border-dark-200/20;
  }
}
