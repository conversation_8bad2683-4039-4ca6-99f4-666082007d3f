import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { useAuth } from '../contexts/AuthContext.jsx'
import { Eye, EyeOff, Mail, Lock, Music, Bot, Sparkles, Zap } from 'lucide-react'
import LoadingSpinner from '../components/LoadingSpinner.jsx'

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false)
  const { login, isLoading } = useAuth()
  const navigate = useNavigate()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const onSubmit = async (data) => {
    const result = await login(data)
    if (result.success) {
      navigate('/dashboard')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-tunami-50 via-white to-ai-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-tunami-200 to-ai-200 rounded-full opacity-20 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-electric-200 to-tunami-200 rounded-full opacity-20 animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-ai-100 to-electric-100 rounded-full opacity-10 animate-pulse"></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        {/* Header */}
        <div className="text-center animate-fade-in-up">
          <div className="flex justify-center items-center mb-6">
            <div className="relative">
              <Music className="w-12 h-12 text-tunami-600" />
              <Bot className="w-4 h-4 text-electric-500 absolute -top-1 -right-1 animate-pulse" />
            </div>
          </div>

          <h1 className="text-5xl font-bold text-gradient mb-2">
            TUNAMI
          </h1>
          <p className="text-sm text-gray-600 mb-4">
            For Entertaining Humanity, Created by AI
          </p>

          <div className="flex items-center justify-center space-x-2 mb-6">
            <Sparkles className="w-4 h-4 text-ai-500 animate-pulse" />
            <h2 className="text-2xl font-semibold text-dark-800">
              Welcome back
            </h2>
            <Zap className="w-4 h-4 text-electric-500 animate-pulse" />
          </div>

          <p className="text-gray-600">
            Sign in to continue your AI music journey
          </p>
        </div>

        {/* Login Form */}
        <div className="card-gradient animate-slide-up">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="label">
                Email address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-tunami-400" />
                </div>
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  className={`input pl-12 ${errors.email ? 'input-error' : ''}`}
                  placeholder="Enter your email"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Please enter a valid email address',
                    },
                  })}
                />
              </div>
              {errors.email && (
                <p className="error-text">{errors.email.message}</p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="label">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-tunami-400" />
                </div>
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  className={`input pl-12 pr-12 ${errors.password ? 'input-error' : ''}`}
                  placeholder="Enter your password"
                  {...register('password', {
                    required: 'Password is required',
                  })}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-4 flex items-center hover:scale-110 transition-transform duration-200"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-tunami-400 hover:text-tunami-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-tunami-400 hover:text-tunami-600" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="error-text">{errors.password.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full btn-primary btn-lg flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Signing in...</span>
                </>
              ) : (
                <>
                  <span>Sign in to TUNAMI</span>
                  <Sparkles className="w-4 h-4" />
                </>
              )}
            </button>
          </form>

          {/* Register Link */}
          <div className="mt-8 text-center">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500">New to TUNAMI?</span>
              </div>
            </div>

            <div className="mt-4">
              <Link
                to="/register"
                className="btn-outline w-full flex items-center justify-center space-x-2"
              >
                <span>Create your account</span>
                <Bot className="w-4 h-4" />
              </Link>
            </div>
          </div>
        </div>

        {/* Features Preview */}
        <div className="grid grid-cols-3 gap-4 mt-8 animate-fade-in">
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-tunami-100 to-tunami-200 rounded-xl flex items-center justify-center mx-auto mb-2">
              <Music className="w-6 h-6 text-tunami-600" />
            </div>
            <p className="text-xs text-gray-600">Discover AI Music</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-ai-100 to-ai-200 rounded-xl flex items-center justify-center mx-auto mb-2">
              <Bot className="w-6 h-6 text-ai-600" />
            </div>
            <p className="text-xs text-gray-600">AI-Powered Creation</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-electric-100 to-electric-200 rounded-xl flex items-center justify-center mx-auto mb-2">
              <Zap className="w-6 h-6 text-electric-600" />
            </div>
            <p className="text-xs text-gray-600">Community Driven</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
