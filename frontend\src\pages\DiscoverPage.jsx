import React, { useState, useCallback } from 'react'
import { Search, Filter, Music, TrendingUp, Clock, X, Bot, Sparkles } from 'lucide-react'
import TrackList from '../components/TrackList'
import MusicPlayer from '../components/MusicPlayer'
import Leaderboards from '../components/Leaderboards'

const DiscoverPage = () => {
  const [currentTrack, setCurrentTrack] = useState(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedGenre, setSelectedGenre] = useState('')
  const [sortBy, setSortBy] = useState('recent')
  const [selectedTags, setSelectedTags] = useState([])
  const [filters, setFilters] = useState({})
  const [refreshKey, setRefreshKey] = useState(0)

  const genres = [
    'All Genres',
    'Electronic',
    'Ambient',
    'Classical',
    'Jazz',
    'Rock',
    'Pop',
    'Hip Hop',
    'Experimental',
    'Other'
  ]

  const sortOptions = [
    { value: 'recent', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'popular', label: 'Most Popular' },
    { value: 'title', label: 'Alphabetical' }
  ]

  const popularTags = [
    'ambient', 'electronic', 'chill', 'experimental', 'classical',
    'jazz', 'synthwave', 'lo-fi', 'orchestral', 'minimal'
  ]

  const handlePlay = (track) => {
    if (currentTrack?.trackId === track.trackId) {
      setIsPlaying(true)
    } else {
      setCurrentTrack(track)
      setIsPlaying(true)
    }
  }

  const handlePause = () => {
    setIsPlaying(false)
  }

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handleTrackEnd = () => {
    setIsPlaying(false)
  }

  // Update filters and refresh track list
  const updateFilters = useCallback(() => {
    const newFilters = {}

    if (searchQuery.trim()) {
      newFilters.search = searchQuery.trim()
    }

    if (selectedGenre && selectedGenre !== 'All Genres') {
      newFilters.genre = selectedGenre
    }

    if (selectedTags.length > 0) {
      newFilters.tags = selectedTags.join(',')
    }

    newFilters.sortBy = sortBy

    setFilters(newFilters)
    setRefreshKey(prev => prev + 1) // Force TrackList to refresh
  }, [searchQuery, selectedGenre, selectedTags, sortBy])

  const handleSearch = (e) => {
    e.preventDefault()
    updateFilters()
  }

  const handleGenreChange = (genre) => {
    setSelectedGenre(genre)
    // Auto-update filters when genre changes
    setTimeout(updateFilters, 0)
  }

  const handleSortChange = (newSortBy) => {
    setSortBy(newSortBy)
    // Auto-update filters when sort changes
    setTimeout(updateFilters, 0)
  }

  const addTag = (tag) => {
    if (!selectedTags.includes(tag)) {
      const newTags = [...selectedTags, tag]
      setSelectedTags(newTags)
      // Auto-update filters when tags change
      setTimeout(updateFilters, 0)
    }
  }

  const removeTag = (tag) => {
    const newTags = selectedTags.filter(t => t !== tag)
    setSelectedTags(newTags)
    // Auto-update filters when tags change
    setTimeout(updateFilters, 0)
  }

  const clearAllFilters = () => {
    setSearchQuery('')
    setSelectedGenre('')
    setSelectedTags([])
    setSortBy('recent')
    setFilters({})
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-tunami-50/30">
      {/* Header */}
      <div className="bg-gradient-to-r from-white/80 to-tunami-50/50 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8">
            <div className="flex items-center gap-4 mb-8 animate-fade-in-up">
              <div className="relative">
                <Music className="w-10 h-10 text-tunami-600" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-electric-400 to-electric-500 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-bold text-gradient">Discover Music</h1>
                <p className="text-gray-600 text-lg">Explore AI-generated music from creators around the world</p>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row gap-4 animate-slide-up">
              {/* Search Bar */}
              <form onSubmit={handleSearch} className="flex-1">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-tunami-400 w-5 h-5" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search tracks, artists, or AI tools..."
                    className="input pl-12 pr-4 text-lg h-12 shadow-tunami"
                  />
                </div>
              </form>

              {/* Genre Filter */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-xl border border-white/20">
                  <Filter className="w-5 h-5 text-tunami-500" />
                  <span className="text-sm font-medium text-dark-700">Genre</span>
                </div>
                <select
                  value={selectedGenre}
                  onChange={(e) => handleGenreChange(e.target.value)}
                  className="input w-auto min-w-[140px] h-12 shadow-tunami"
                >
                  {genres.map((genre) => (
                    <option key={genre} value={genre === 'All Genres' ? '' : genre}>
                      {genre}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort Options */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-xl border border-white/20">
                  <TrendingUp className="w-5 h-5 text-electric-500" />
                  <span className="text-sm font-medium text-dark-700">Sort</span>
                </div>
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="input w-auto min-w-[140px] h-12 shadow-tunami"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Active Filters */}
            {(searchQuery || selectedGenre || selectedTags.length > 0) && (
              <div className="mt-6 flex flex-wrap items-center gap-3 animate-fade-in">
                <span className="text-sm font-medium text-dark-600 flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  Active filters:
                </span>

                {searchQuery && (
                  <span className="bg-gradient-to-r from-tunami-100 to-tunami-200 text-tunami-800 px-4 py-2 rounded-full text-sm flex items-center gap-2 shadow-tunami">
                    <Search className="w-3 h-3" />
                    Search: "{searchQuery}"
                    <button
                      onClick={() => { setSearchQuery(''); updateFilters(); }}
                      className="hover:scale-110 transition-transform duration-200"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}

                {selectedGenre && (
                  <span className="bg-gradient-to-r from-electric-100 to-electric-200 text-electric-800 px-4 py-2 rounded-full text-sm flex items-center gap-2 shadow-electric">
                    <Music className="w-3 h-3" />
                    Genre: {selectedGenre}
                    <button
                      onClick={() => handleGenreChange('')}
                      className="hover:scale-110 transition-transform duration-200"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}

                {selectedTags.map(tag => (
                  <span key={tag} className="bg-gradient-to-r from-ai-100 to-ai-200 text-ai-800 px-4 py-2 rounded-full text-sm flex items-center gap-2 shadow-ai">
                    #{tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="hover:scale-110 transition-transform duration-200"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}

                <button
                  onClick={clearAllFilters}
                  className="text-sm text-dark-500 hover:text-dark-700 underline font-medium transition-colors duration-200"
                >
                  Clear all
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Featured Section */}
            <div className="animate-fade-in-up">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-electric-500 to-electric-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gradient-ai">Trending Now</h2>
              </div>

              <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-tunami-600 via-ai-600 to-electric-600 p-8 text-white shadow-2xl">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
                  <div className="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white rounded-full"></div>
                </div>

                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <Music className="w-8 h-8" />
                    <h3 className="text-2xl font-bold">Welcome to TUNAMI</h3>
                  </div>

                  <p className="text-white/90 text-lg mb-6 leading-relaxed">
                    Discover amazing AI-generated music from talented creators around the world.
                    Upload your own tracks and join our vibrant community of AI music enthusiasts!
                  </p>

                  <div className="flex flex-wrap gap-3">
                    <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                      <Music className="w-4 h-4" />
                      <span className="font-medium">1000+ AI Tracks</span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                      <Bot className="w-4 h-4" />
                      <span className="font-medium">AI-Powered Creation</span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                      <Sparkles className="w-4 h-4" />
                      <span className="font-medium">Community-Driven</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Track List */}
            <div className="card-gradient animate-slide-up">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-tunami-500 to-tunami-600 rounded-xl flex items-center justify-center">
                    <Clock className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-dark-800">Latest Tracks</h2>
                </div>

                <div className="flex items-center gap-2 text-sm text-dark-600">
                  <div className="w-2 h-2 bg-electric-500 rounded-full animate-pulse"></div>
                  <span>Live updates</span>
                </div>
              </div>

              <TrackList
                key={refreshKey} // Force refresh when filters change
                filters={filters}
                currentTrack={currentTrack}
                isPlaying={isPlaying}
                onPlay={handlePlay}
                onPause={handlePause}
              />
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Music Player */}
            {currentTrack && (
              <div className="sticky top-6 animate-slide-left">
                <MusicPlayer
                  track={currentTrack}
                  isPlaying={isPlaying}
                  onPlayPause={handlePlayPause}
                  onTrackEnd={handleTrackEnd}
                />
              </div>
            )}

            {/* Popular Genres */}
            <div className="card-gradient animate-fade-in">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-electric-500 to-electric-600 rounded-xl flex items-center justify-center">
                  <Music className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-dark-800">Popular Genres</h3>
              </div>

              <div className="space-y-2">
                {genres.slice(1).map((genre, index) => (
                  <button
                    key={genre}
                    onClick={() => handleGenreChange(genre)}
                    className={`w-full text-left px-4 py-3 rounded-xl transition-all duration-300 transform hover:scale-[1.02] ${
                      selectedGenre === genre
                        ? 'bg-gradient-to-r from-tunami-100 to-tunami-200 text-tunami-800 shadow-tunami'
                        : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 text-dark-700 hover:text-dark-900'
                    }`}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{genre}</span>
                      {selectedGenre === genre && (
                        <div className="w-2 h-2 bg-tunami-500 rounded-full animate-pulse"></div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Popular Tags */}
            <div className="card-gradient animate-fade-in">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-ai-500 to-ai-600 rounded-xl flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-dark-800">Popular Tags</h3>
              </div>

              <div className="flex flex-wrap gap-2">
                {popularTags.map((tag, index) => (
                  <button
                    key={tag}
                    onClick={() => addTag(tag)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                      selectedTags.includes(tag)
                        ? 'bg-gradient-to-r from-ai-100 to-ai-200 text-ai-800 cursor-default shadow-ai'
                        : 'bg-gradient-to-r from-gray-100 to-gray-200 text-dark-700 hover:from-gray-200 hover:to-gray-300 hover:text-dark-900'
                    }`}
                    disabled={selectedTags.includes(tag)}
                    style={{ animationDelay: `${index * 0.05}s` }}
                  >
                    #{tag}
                  </button>
                ))}
              </div>
            </div>

            {/* Leaderboards */}
            <Leaderboards />

            {/* AI Tools Spotlight */}
            <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">AI Tools Spotlight</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs font-bold">AI</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">AIVA</p>
                    <p className="text-sm text-gray-600">Classical & Orchestral</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs font-bold">AM</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Amper Music</p>
                    <p className="text-sm text-gray-600">Pop & Electronic</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs font-bold">SR</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Soundraw</p>
                    <p className="text-sm text-gray-600">Ambient & Chill</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Community Stats */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Community</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Tracks</span>
                  <span className="font-semibold">1,234</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Creators</span>
                  <span className="font-semibold">89</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Plays</span>
                  <span className="font-semibold">45.6K</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DiscoverPage
