<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TUNAMI - Modern UI Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        tunami: {
                            50: '#f0f4ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                            950: '#1e1b4b',
                        },
                        electric: {
                            50: '#ecfeff',
                            100: '#cffafe',
                            200: '#a5f3fc',
                            300: '#67e8f9',
                            400: '#22d3ee',
                            500: '#06b6d4',
                            600: '#0891b2',
                            700: '#0e7490',
                            800: '#155e75',
                            900: '#164e63',
                            950: '#083344',
                        },
                        ai: {
                            50: '#faf5ff',
                            100: '#f3e8ff',
                            200: '#e9d5ff',
                            300: '#d8b4fe',
                            400: '#c084fc',
                            500: '#a855f7',
                            600: '#9333ea',
                            700: '#7c3aed',
                            800: '#6b21a8',
                            900: '#581c87',
                            950: '#3b0764',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'float': 'float 3s ease-in-out infinite',
                        'pulse-glow': 'pulseGlow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        pulseGlow: {
                            '0%': { boxShadow: '0 0 5px rgba(99, 102, 241, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(99, 102, 241, 0.8)' },
                        },
                    },
                    backgroundImage: {
                        'tunami-gradient': 'linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #22d3ee 100%)',
                        'ai-gradient': 'linear-gradient(135deg, #a855f7 0%, #6366f1 100%)',
                        'electric-gradient': 'linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%)',
                    }
                }
            }
        }
    </script>
    <style>
        .text-gradient {
            background: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #22d3ee 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 via-white to-tunami-50 min-h-screen">
    <!-- Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-tunami-200 to-ai-200 rounded-full opacity-20 animate-float"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-electric-200 to-tunami-200 rounded-full opacity-20 animate-float" style="animation-delay: 1s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-ai-100 to-electric-100 rounded-full opacity-10 animate-pulse"></div>
    </div>

    <!-- Header -->
    <header class="relative z-10 glass sticky top-0">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center group">
                    <div class="relative">
                        <svg class="w-8 h-8 text-tunami-600 group-hover:text-tunami-700 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                        </svg>
                        <svg class="w-3 h-3 text-electric-500 absolute -top-1 -right-1 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-2xl font-bold text-gradient">TUNAMI</h1>
                        <p class="text-xs text-gray-500 -mt-1">For Entertaining Humanity, Created by AI</p>
                    </div>
                    <div class="ml-3 flex items-center space-x-1">
                        <span class="px-2 py-1 text-xs font-semibold bg-gradient-to-r from-tunami-100 to-ai-100 text-tunami-700 rounded-full border border-tunami-200">MVP</span>
                        <svg class="w-3 h-3 text-ai-500 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-2">
                    <a href="#" class="flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium bg-gradient-to-r from-tunami-100 to-tunami-200 text-tunami-800 shadow-lg transition-all duration-300 hover:scale-105">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <span>Discover</span>
                    </a>
                    <a href="#" class="flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium text-dark-600 hover:text-dark-900 hover:bg-gray-100 transition-all duration-300 hover:scale-105">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                    <a href="#" class="flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium text-dark-600 hover:text-dark-900 hover:bg-gray-100 transition-all duration-300 hover:scale-105">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <span>Creator Studio</span>
                    </a>
                </nav>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <button class="px-4 py-2 bg-gradient-to-r from-tunami-600 to-tunami-700 text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg">
                        Subscribe
                    </button>
                    <div class="flex items-center space-x-2 text-sm text-dark-700 glass px-3 py-2 rounded-xl">
                        <div class="w-6 h-6 rounded-full bg-gradient-to-r from-tunami-400 to-ai-500 flex items-center justify-center">
                            <svg class="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <span class="font-medium">Welcome, User</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-12 animate-fade-in-up">
            <div class="flex justify-center items-center mb-6">
                <div class="relative">
                    <svg class="w-16 h-16 text-tunami-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-electric-400 to-electric-500 rounded-full flex items-center justify-center">
                        <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>
            
            <h1 class="text-6xl font-bold text-gradient mb-4">TUNAMI</h1>
            <p class="text-xl text-gray-600 mb-8">For Entertaining Humanity, Created by AI</p>
            
            <div class="flex items-center justify-center space-x-4 mb-8">
                <svg class="w-6 h-6 text-ai-500 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <h2 class="text-3xl font-semibold text-dark-800">Discover AI Music</h2>
                <svg class="w-6 h-6 text-electric-500 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
            </div>
            
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Explore amazing AI-generated music from talented creators around the world. 
                Upload your own tracks and join our vibrant community of AI music enthusiasts!
            </p>
        </div>

        <!-- Feature Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <!-- Create AI Music -->
            <div class="glass rounded-2xl p-8 text-center transition-all duration-300 hover:scale-105 animate-slide-up">
                <div class="w-16 h-16 bg-gradient-to-br from-tunami-100 to-tunami-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-tunami-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-dark-800 mb-2">Create AI Music</h3>
                <p class="text-gray-600">Use cutting-edge AI tools to compose unique tracks and share them with the world.</p>
            </div>

            <!-- AI-Powered Tools -->
            <div class="glass rounded-2xl p-8 text-center transition-all duration-300 hover:scale-105 animate-slide-up" style="animation-delay: 0.1s;">
                <div class="w-16 h-16 bg-gradient-to-br from-ai-100 to-ai-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-ai-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-dark-800 mb-2">AI-Powered Creation</h3>
                <p class="text-gray-600">Leverage advanced AI algorithms to enhance your musical creativity and production.</p>
            </div>

            <!-- Community Driven -->
            <div class="glass rounded-2xl p-8 text-center transition-all duration-300 hover:scale-105 animate-slide-up" style="animation-delay: 0.2s;">
                <div class="w-16 h-16 bg-gradient-to-br from-electric-100 to-electric-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-electric-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-dark-800 mb-2">Share & Earn</h3>
                <p class="text-gray-600">Build your audience, receive tips, and monetize your AI-generated musical creations.</p>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="text-center">
            <div class="glass rounded-3xl p-12 bg-gradient-to-br from-tunami-600 via-ai-600 to-electric-600 text-white relative overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
                    <div class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full"></div>
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white rounded-full"></div>
                </div>
                
                <div class="relative z-10">
                    <h2 class="text-4xl font-bold mb-4">Ready to Start Creating?</h2>
                    <p class="text-xl mb-8 opacity-90">Join thousands of AI music creators on TUNAMI today!</p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button class="px-8 py-4 bg-white text-tunami-700 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl">
                            Get Started Free
                        </button>
                        <button class="px-8 py-4 border-2 border-white text-white rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:bg-white hover:text-tunami-700">
                            Explore Music
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="relative z-10 glass mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-tunami-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                        </svg>
                        <span class="font-bold text-gradient">TUNAMI</span>
                    </div>
                    <span class="text-sm text-gray-500">For Entertaining Humanity, Created by AI</span>
                </div>
                
                <div class="flex items-center space-x-6 text-sm text-gray-500">
                    <span>© 2024 TUNAMI MVP</span>
                    <div class="flex items-center space-x-2">
                        <span>Built with</span>
                        <div class="flex items-center space-x-1">
                            <span class="px-2 py-1 bg-gradient-to-r from-orange-100 to-orange-200 text-orange-700 rounded-full text-xs font-medium">AWS SAM</span>
                            <span class="px-2 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 rounded-full text-xs font-medium">React</span>
                            <span class="px-2 py-1 bg-gradient-to-r from-ai-100 to-ai-200 text-ai-700 rounded-full text-xs font-medium">AI</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
