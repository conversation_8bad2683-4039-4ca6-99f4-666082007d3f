import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext.jsx'
import { LogOut, User, Music, Compass, Upload, Home, Shield, Sparkles, Bot } from 'lucide-react'
import SubscriptionButton from './SubscriptionButton'

const Layout = ({ children }) => {
  const { user, logout, isAdmin } = useAuth()
  const location = useLocation()

  const handleLogout = () => {
    logout()
  }

  const navigation = [
    { name: 'Discover', href: '/discover', icon: Compass },
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Creator Studio', href: '/creator', icon: Upload },
    ...(isAdmin() ? [{ name: 'Admin', href: '/admin', icon: Shield }] : []),
  ]

  const isActive = (href) => location.pathname === href

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-tunami-50/30">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/discover" className="flex items-center group">
              <div className="relative">
                <Music className="w-8 h-8 text-tunami-600 group-hover:text-tunami-700 transition-colors duration-300" />
                <Bot className="w-3 h-3 text-electric-500 absolute -top-1 -right-1 animate-pulse" />
              </div>
              <div className="ml-3">
                <h1 className="text-2xl font-bold text-gradient">
                  TUNAMI
                </h1>
                <p className="text-xs text-gray-500 -mt-1">
                  For Entertaining Humanity, Created by AI
                </p>
              </div>
              <div className="ml-3 flex items-center space-x-1">
                <span className="px-2 py-1 text-xs font-semibold bg-gradient-to-r from-tunami-100 to-ai-100 text-tunami-700 rounded-full border border-tunami-200">
                  MVP
                </span>
                <Sparkles className="w-3 h-3 text-ai-500 animate-pulse" />
              </div>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-2">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={
                      isActive(item.href)
                        ? 'nav-link-active'
                        : 'nav-link-inactive'
                    }
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.name}</span>
                  </Link>
                )
              })}
            </nav>

            {/* User menu */}
            <div className="flex items-center space-x-4">
              <SubscriptionButton
                isSubscribed={false} // TODO: Get from user context
                size="sm"
              />

              <div className="flex items-center space-x-2 text-sm text-dark-700 bg-white/50 backdrop-blur-sm px-3 py-2 rounded-xl border border-white/20">
                <div className="w-6 h-6 rounded-full bg-gradient-to-r from-tunami-400 to-ai-500 flex items-center justify-center">
                  <User className="h-3 w-3 text-white" />
                </div>
                <span className="font-medium">Welcome, {user?.username}</span>
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-dark-600 hover:text-dark-900 hover:bg-white/50 rounded-xl transition-all duration-300 backdrop-blur-sm border border-transparent hover:border-white/20"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="relative">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-white/80 to-tunami-50/50 backdrop-blur-sm border-t border-white/20 mt-auto">
        <div className="container py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Music className="w-5 h-5 text-tunami-600" />
                <span className="font-bold text-gradient">TUNAMI</span>
              </div>
              <span className="text-sm text-gray-500">
                For Entertaining Humanity, Created by AI
              </span>
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <span>© 2024 TUNAMI MVP</span>
              <div className="flex items-center space-x-2">
                <span>Built with</span>
                <div className="flex items-center space-x-1">
                  <span className="px-2 py-1 bg-gradient-to-r from-orange-100 to-orange-200 text-orange-700 rounded-full text-xs font-medium">
                    AWS SAM
                  </span>
                  <span className="px-2 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 rounded-full text-xs font-medium">
                    React
                  </span>
                  <span className="px-2 py-1 bg-gradient-to-r from-ai-100 to-ai-200 text-ai-700 rounded-full text-xs font-medium">
                    AI
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Layout
