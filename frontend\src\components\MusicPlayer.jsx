import React, { useState, useRef, useEffect } from 'react'
import { Play, Pause, Volume2, VolumeX, Skip<PERSON>ack, Ski<PERSON><PERSON>or<PERSON>, Music, Heart, Eye } from 'lucide-react'

const MusicPlayer = ({ 
  track, 
  isPlaying, 
  onPlayPause, 
  onTrackEnd,
  className = '' 
}) => {
  const audioRef = useRef(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Update audio element when track changes
  useEffect(() => {
    if (audioRef.current && track) {
      audioRef.current.src = track.audioFileUrl
      audioRef.current.load()
      setCurrentTime(0)
      setIsLoading(true)
    }
  }, [track])

  // Handle play/pause state changes
  useEffect(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.play().catch(console.error)
    } else {
      audioRef.current.pause()
    }
  }, [isPlaying])

  // Audio event handlers
  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
      setIsLoading(false)
    }
  }

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }

  const handleEnded = () => {
    setCurrentTime(0)
    if (onTrackEnd) {
      onTrackEnd()
    }
  }

  const handleLoadStart = () => {
    setIsLoading(true)
  }

  const handleCanPlay = () => {
    setIsLoading(false)
  }

  // Control handlers
  const handleSeek = (e) => {
    if (!audioRef.current || !duration) return

    const rect = e.currentTarget.getBoundingClientRect()
    const percent = (e.clientX - rect.left) / rect.width
    const newTime = percent * duration

    audioRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value)
    setVolume(newVolume)
    if (audioRef.current) {
      audioRef.current.volume = newVolume
    }
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    if (audioRef.current) {
      if (isMuted) {
        audioRef.current.volume = volume
        setIsMuted(false)
      } else {
        audioRef.current.volume = 0
        setIsMuted(true)
      }
    }
  }

  const formatTime = (time) => {
    if (!time || !isFinite(time)) return '0:00'
    
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const progressPercent = duration ? (currentTime / duration) * 100 : 0

  if (!track) {
    return (
      <div className={`card-gradient p-6 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-tunami-200 to-ai-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Music className="w-8 h-8 text-tunami-600" />
          </div>
          <p className="text-dark-500 font-medium">No track selected</p>
          <p className="text-sm text-dark-400 mt-1">Choose a track to start playing</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`card-gradient overflow-hidden ${className}`}>
      {/* Hidden audio element */}
      <audio
        ref={audioRef}
        onLoadedMetadata={handleLoadedMetadata}
        onTimeUpdate={handleTimeUpdate}
        onEnded={handleEnded}
        onLoadStart={handleLoadStart}
        onCanPlay={handleCanPlay}
        preload="metadata"
      />

      {/* Track Info */}
      <div className="p-6 border-b border-white/20">
        <div className="flex items-center gap-4">
          {track.coverImageUrl ? (
            <img
              src={track.coverImageUrl}
              alt={track.title}
              className="w-16 h-16 rounded-2xl object-cover shadow-lg"
            />
          ) : (
            <div className="w-16 h-16 bg-gradient-to-br from-tunami-500 via-ai-500 to-electric-500 rounded-2xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-xl">
                {track.title.charAt(0).toUpperCase()}
              </span>
            </div>
          )}

          <div className="flex-1 min-w-0">
            <h3 className="font-bold text-dark-900 truncate text-lg">
              {track.title}
            </h3>
            <p className="text-dark-600 truncate font-medium">
              {track.creator?.username || 'Unknown Artist'}
            </p>
            <div className="flex items-center gap-2 mt-1">
              <span className="px-2 py-1 bg-gradient-to-r from-tunami-100 to-tunami-200 text-tunami-700 rounded-full text-xs font-medium">
                {track.genre}
              </span>
              {track.aiTool && (
                <span className="px-2 py-1 bg-gradient-to-r from-ai-100 to-ai-200 text-ai-700 rounded-full text-xs font-medium">
                  {track.aiTool}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="p-6">
        {/* Progress Bar */}
        <div className="mb-6">
          <div
            className="progress-bar cursor-pointer group"
            onClick={handleSeek}
          >
            <div
              className="progress-fill"
              style={{ width: `${progressPercent}%` }}
            />
          </div>
          <div className="flex justify-between text-sm text-dark-600 mt-2 font-medium">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Previous Track (placeholder) */}
            <button
              className="p-2 text-dark-400 hover:text-dark-600 disabled:opacity-50 transition-all duration-200 hover:scale-110"
              disabled
            >
              <SkipBack className="w-5 h-5" />
            </button>

            {/* Play/Pause */}
            <button
              onClick={onPlayPause}
              disabled={isLoading}
              className="player-control"
            >
              {isLoading ? (
                <div className="loading-spinner w-6 h-6" />
              ) : isPlaying ? (
                <Pause className="w-6 h-6" />
              ) : (
                <Play className="w-6 h-6 ml-0.5" />
              )}
            </button>

            {/* Next Track (placeholder) */}
            <button
              className="p-2 text-dark-400 hover:text-dark-600 disabled:opacity-50 transition-all duration-200 hover:scale-110"
              disabled
            >
              <SkipForward className="w-5 h-5" />
            </button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center gap-3">
            <button
              onClick={toggleMute}
              className="p-2 text-dark-500 hover:text-dark-700 transition-all duration-200 hover:scale-110"
            >
              {isMuted || volume === 0 ? (
                <VolumeX className="w-5 h-5" />
              ) : (
                <Volume2 className="w-5 h-5" />
              )}
            </button>
            <div className="relative">
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-24 h-2 bg-gray-200 rounded-full appearance-none cursor-pointer slider"
                style={{
                  background: `linear-gradient(to right, #6366f1 0%, #6366f1 ${(isMuted ? 0 : volume) * 100}%, #e5e7eb ${(isMuted ? 0 : volume) * 100}%, #e5e7eb 100%)`
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Track Stats */}
      {track.listenCount !== undefined && (
        <div className="px-6 pb-6 border-t border-white/20">
          <div className="flex items-center justify-between pt-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-electric-100 to-electric-200 rounded-full flex items-center justify-center">
                  <Eye className="w-3 h-3 text-electric-600" />
                </div>
                <span className="text-sm font-medium text-dark-600">{track.listenCount} plays</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-red-100 to-red-200 rounded-full flex items-center justify-center">
                  <Heart className="w-3 h-3 text-red-500" />
                </div>
                <span className="text-sm font-medium text-dark-600">{track.likeCount} likes</span>
              </div>
            </div>

            {track.createdAt && (
              <span className="text-xs text-dark-400 font-medium">
                {new Date(track.createdAt).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default MusicPlayer
