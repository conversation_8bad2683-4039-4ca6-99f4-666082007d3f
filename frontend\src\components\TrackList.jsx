import React, { useState, useEffect } from 'react'
import { Play, Pause, Heart, MessageCircle, Clock, User, Bot, Sparkles, Eye, Music } from 'lucide-react'
import apiService from '../services/apiService'
import { useAuth } from '../contexts/AuthContext'
import LoadingSpinner from './LoadingSpinner'
import LikeButton from './LikeButton'
import CommentSection from './CommentSection'
import TipButton from './TipButton'
import ReportButton from './ReportButton'
import toast from 'react-hot-toast'

const TrackCard = ({ track, isPlaying, onPlay, onPause, currentTrack }) => {
  const { user } = useAuth()
  const isCurrentTrack = currentTrack?.trackId === track.trackId

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handlePlayPause = () => {
    if (isCurrentTrack && isPlaying) {
      onPause()
    } else {
      onPlay(track)
    }
  }

  return (
    <div className="track-card group">
      <div className="p-6">
        {/* Track Header */}
        <div className="flex items-start gap-6 mb-4">
          {/* Cover Art */}
          <div className="relative">
            {track.coverImageUrl ? (
              <img
                src={track.coverImageUrl}
                alt={track.title}
                className="track-card-image object-cover"
              />
            ) : (
              <div className="track-card-image">
                <span className="text-white font-bold text-xl">
                  {track.title.charAt(0).toUpperCase()}
                </span>
              </div>
            )}

            {/* Play Button Overlay */}
            <button
              onClick={handlePlayPause}
              className="absolute inset-0 bg-black/60 backdrop-blur-sm rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-95 group-hover:scale-100"
            >
              {isCurrentTrack && isPlaying ? (
                <Pause className="w-8 h-8 text-white drop-shadow-lg" />
              ) : (
                <Play className="w-8 h-8 text-white ml-1 drop-shadow-lg" />
              )}
            </button>

            {/* AI Badge */}
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-ai-500 to-ai-600 rounded-full flex items-center justify-center shadow-lg">
              <Bot className="w-3 h-3 text-white" />
            </div>
          </div>

          {/* Track Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-bold text-dark-900 text-xl mb-1 truncate group-hover:text-tunami-700 transition-colors duration-300">
                {track.title}
              </h3>
              <div className="flex items-center gap-1 text-xs text-dark-500">
                <Sparkles className="w-3 h-3" />
                <span>AI</span>
              </div>
            </div>

            <div className="flex items-center gap-3 text-sm text-dark-600 mb-3">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-tunami-100 to-tunami-200 rounded-full flex items-center justify-center">
                  <User className="w-3 h-3 text-tunami-600" />
                </div>
                <span className="font-medium">{track.creator?.username || 'Unknown Artist'}</span>
              </div>
              <span className="text-dark-400">•</span>
              <span className="px-3 py-1 bg-gradient-to-r from-tunami-100 to-tunami-200 text-tunami-700 rounded-full text-xs font-medium">
                {track.genre}
              </span>
            </div>

            <div className="flex items-center gap-6 text-sm text-dark-500">
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                  <Clock className="w-3 h-3 text-gray-500" />
                </div>
                <span>{formatDate(track.uploadDate)}</span>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-gradient-to-r from-electric-100 to-electric-200 rounded-full flex items-center justify-center">
                  <Eye className="w-3 h-3 text-electric-600" />
                </div>
                <span>{track.listenCount || 0} plays</span>
              </div>
            </div>
          </div>

          {/* Play Button */}
          <button
            onClick={handlePlayPause}
            className={`player-control ${
              isCurrentTrack && isPlaying
                ? 'shadow-glow'
                : 'bg-gradient-to-r from-gray-100 to-gray-200 text-dark-600 hover:from-tunami-100 hover:to-tunami-200 hover:text-tunami-700'
            }`}
          >
            {isCurrentTrack && isPlaying ? (
              <Pause className="w-6 h-6" />
            ) : (
              <Play className="w-6 h-6 ml-0.5" />
            )}
          </button>
        </div>

        {/* Description */}
        {track.description && (
          <div className="mb-4">
            <p className="text-dark-600 text-sm leading-relaxed line-clamp-2">
              {track.description}
            </p>
          </div>
        )}

        {/* AI Tools Used */}
        {track.aiToolsUsed && track.aiToolsUsed.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Bot className="w-4 h-4 text-ai-500" />
              <p className="text-xs font-semibold text-dark-600 uppercase tracking-wide">AI Tools Used:</p>
            </div>
            <div className="flex flex-wrap gap-2">
              {track.aiToolsUsed.map((tool, index) => (
                <span
                  key={index}
                  className="bg-gradient-to-r from-ai-100 to-ai-200 text-ai-800 text-xs px-3 py-1.5 rounded-full font-medium shadow-ai"
                >
                  {tool}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {track.tags && track.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {track.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-gradient-to-r from-gray-100 to-gray-200 text-dark-700 text-xs px-3 py-1.5 rounded-full font-medium hover:from-electric-100 hover:to-electric-200 hover:text-electric-700 transition-all duration-300 cursor-pointer"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}

        {/* Social Features */}
        <div className="border-t border-white/20 pt-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <LikeButton
                trackId={track.trackId}
                initialLikeCount={track.likeCount || 0}
                initialIsLiked={false} // TODO: Get user's like status from API
                size="sm"
              />
              <TipButton
                trackId={track.trackId}
                creatorId={track.creatorId}
                creatorUsername={track.creator?.username || 'Unknown Artist'}
                trackTitle={track.title}
                size="sm"
                variant="minimal"
              />
            </div>

            {/* Report Button */}
            <div className="flex items-center">
              <ReportButton
                reportType="content"
                targetId={track.trackId}
                targetInfo={{
                  title: track.title,
                  artist: track.creator?.username || 'Unknown Artist'
                }}
                variant="icon"
              />
            </div>
          </div>

          <CommentSection
            trackId={track.trackId}
            initialCommentCount={track.commentCount || 0}
          />
        </div>
      </div>
    </div>
  )
}

const TrackList = ({
  creatorId = null,
  filters = {},
  onTrackSelect,
  currentTrack,
  isPlaying,
  onPlay,
  onPause,
  className = ''
}) => {
  const [tracks, setTracks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [pagination, setPagination] = useState({
    hasMore: false,
    nextToken: null
  })
  const [loadingMore, setLoadingMore] = useState(false)

  const loadTracks = async (nextToken = null) => {
    try {
      const params = {
        limit: 20,
        ...(creatorId && { creatorId }),
        ...(nextToken && { lastEvaluatedKey: nextToken }),
        ...filters // Include all filters from props
      }

      const response = await apiService.listTracks(params)
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to load tracks')
      }

      const newTracks = response.data.tracks || []
      
      if (nextToken) {
        setTracks(prev => [...prev, ...newTracks])
      } else {
        setTracks(newTracks)
      }

      setPagination({
        hasMore: response.data.pagination?.hasMore || false,
        nextToken: response.data.pagination?.nextToken || null
      })

    } catch (error) {
      console.error('Load tracks error:', error)
      setError(error.message)
      toast.error(error.message || 'Failed to load tracks')
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  useEffect(() => {
    setLoading(true)
    loadTracks()
  }, [creatorId, filters])

  const handleLoadMore = async () => {
    if (loadingMore || !pagination.hasMore) return
    
    setLoadingMore(true)
    await loadTracks(pagination.nextToken)
  }

  if (loading) {
    return (
      <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
        <div className="w-16 h-16 bg-gradient-to-br from-tunami-200 to-ai-200 rounded-2xl flex items-center justify-center mb-4 animate-pulse">
          <Music className="w-8 h-8 text-tunami-600" />
        </div>
        <LoadingSpinner />
        <p className="text-dark-500 mt-4 font-medium">Loading amazing AI tracks...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="w-16 h-16 bg-gradient-to-br from-red-200 to-red-300 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <Music className="w-8 h-8 text-red-600" />
        </div>
        <p className="text-red-600 mb-6 font-medium">{error}</p>
        <button
          onClick={() => {
            setError(null)
            setLoading(true)
            loadTracks()
          }}
          className="btn-primary"
        >
          Try Again
        </button>
      </div>
    )
  }

  if (tracks.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="w-20 h-20 bg-gradient-to-br from-tunami-200 to-ai-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <Music className="w-10 h-10 text-tunami-600" />
        </div>
        <h3 className="text-xl font-bold text-dark-800 mb-2">
          {creatorId ? 'No tracks uploaded yet' : 'No tracks available'}
        </h3>
        <p className="text-dark-500">
          {creatorId
            ? 'Start creating amazing AI music and share it with the world!'
            : 'Be the first to upload AI-generated music to TUNAMI!'
          }
        </p>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {tracks.map((track) => (
          <TrackCard
            key={track.trackId}
            track={track}
            isPlaying={isPlaying}
            onPlay={onPlay}
            onPause={onPause}
            currentTrack={currentTrack}
          />
        ))}
      </div>

      {/* Load More Button */}
      {pagination.hasMore && (
        <div className="text-center mt-8">
          <button
            onClick={handleLoadMore}
            disabled={loadingMore}
            className="btn-outline btn-lg flex items-center gap-3 mx-auto"
          >
            {loadingMore && <LoadingSpinner size="sm" />}
            <span>Load More AI Tracks</span>
            <Sparkles className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  )
}

export default TrackList
